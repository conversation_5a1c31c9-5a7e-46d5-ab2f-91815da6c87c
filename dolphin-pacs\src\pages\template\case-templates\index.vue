<template>
  <div class="case-templates-page">
   

    <div class="templates-container">
      <div
        v-for="template in templates"
        :key="template.id"
        class="template-card"
        @mouseenter="hoveredCard = template.id"
        @mouseleave="hoveredCard = null"
      >
        <!-- 卡片内容 -->
        <div class="card-content">
          <!-- 模板缩略图 -->
          <div class="template-thumbnail">
            <img
              :src="template.thumbnail"
              :alt="template.name"
              @error="handleImageError"
            />
            <!-- 分类标签 -->
            <div class="category-tag">
              {{ template.category }}
            </div>
          </div>

          <!-- 模板信息 -->
          <div class="template-info">
            <h3 class="template-name">{{ template.name }}</h3>
            <p class="template-description">{{ template.description }}</p>
          </div>
        </div>

        <!-- 悬浮按钮组 -->
        <transition name="fade">
          <div v-show="hoveredCard === template.id" class="action-buttons">
            <el-button
              type="primary"
              size="small"
              @click="handlePreview(template)"
              class="action-btn preview-btn"
            >
              预览
            </el-button>
            <el-button
              type="success"
              size="small"
              @click="handleDownload(template)"
              class="action-btn download-btn"
            >
              下载
            </el-button>
          </div>
        </transition>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

defineOptions({
  name: "CaseTemplates"
})

// 悬浮状态
const hoveredCard = ref<string | null>(null)

// 模板数据接口
interface Template {
  id: string
  name: string
  description: string
  thumbnail: string
  category: string
  downloadUrl?: string
}

// 模拟模板数据
const templates = ref<Template[]>([
  {
    id: '1',
    name: '心脏超声检查报告',
    description: '适用于心脏超声检查的标准报告模板',
    thumbnail: '/images/templates/cardiac-template.jpg',
    category: '心血管',
    downloadUrl: '/downloads/cardiac-template.docx'
  },
  {
    id: '2',
    name: '腹部超声检查报告',
    description: '适用于腹部器官超声检查的报告模板',
    thumbnail: '/images/templates/abdominal-template.jpg',
    category: '消化系统',
    downloadUrl: '/downloads/abdominal-template.docx'
  },
  {
    id: '3',
    name: '甲状腺超声检查报告',
    description: '专门用于甲状腺超声检查的报告模板',
    thumbnail: '/images/templates/thyroid-template.jpg',
    category: '内分泌',
    downloadUrl: '/downloads/thyroid-template.docx'
  },
  {
    id: '4',
    name: '产科超声检查报告',
    description: '适用于孕期超声检查的专业报告模板',
    thumbnail: '/images/templates/obstetric-template.jpg',
    category: '妇产科',
    downloadUrl: '/downloads/obstetric-template.docx'
  },
  {
    id: '5',
    name: '血管超声检查报告',
    description: '用于血管系统超声检查的报告模板',
    thumbnail: '/images/templates/vascular-template.jpg',
    category: '血管外科',
    downloadUrl: '/downloads/vascular-template.docx'
  },
  {
    id: '6',
    name: '肌骨超声检查报告',
    description: '适用于肌肉骨骼系统超声检查的模板',
    thumbnail: '/images/templates/musculoskeletal-template.jpg',
    category: '骨科',
    downloadUrl: '/downloads/musculoskeletal-template.docx'
  }
])

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/images/templates/default-template.jpg' // 默认图片
}

// 预览模板
const handlePreview = (template: Template) => {
  ElMessage.info(`预览模板：${template.name}`)
  // TODO: 实现预览功能，可能是弹窗或跳转到预览页面
  console.log('预览模板:', template)
}

// 下载模板
const handleDownload = (template: Template) => {
  ElMessage.success(`开始下载：${template.name}`)
  // TODO: 实现下载功能
  console.log('下载模板:', template)
  
  // 模拟下载
  if (template.downloadUrl) {
    const link = document.createElement('a')
    link.href = template.downloadUrl
    link.download = `${template.name}.docx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}
</script>

<style lang="scss" scoped>
.case-templates-page {
  padding: 24px;
  min-height: 100vh;
  background-color: var(--el-bg-color-page);
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
  
  h2 {
    font-size: 28px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin-bottom: 8px;
  }
  
  .page-description {
    font-size: 16px;
    color: var(--el-text-color-regular);
    margin: 0;
  }
}

.templates-container {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  justify-content: center;
  max-width: 1400px;
  margin: 0 auto;
}

// 响应式布局
@media screen and (max-width: 1400px) {
  .templates-container {
    justify-content: flex-start;
  }
}

@media screen and (max-width: 768px) {
  .case-templates-page {
    padding: 16px;
  }
  
  .page-header {
    margin-bottom: 24px;
    
    h2 {
      font-size: 24px;
    }
    
    .page-description {
      font-size: 14px;
    }
  }
  
  .templates-container {
    gap: 16px;
    justify-content: center;
  }
}
</style>
